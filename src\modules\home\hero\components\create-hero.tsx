'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

const CreateHeroPage: React.FC = () => {
    const router = useRouter();
    const [titles, setTitles] = useState<string[]>(['']);
    const [images, setImages] = useState<string[]>([]);

    const handleTitleChange = (index: number, value: string) => {
        const newTitles = [...titles];
        newTitles[index] = value;
        setTitles(newTitles);
    };

    const handleAddTitle = () => {
        setTitles([...titles, '']);
    };

    const handleRemoveTitle = (index: number) => {
        if (titles.length > 1) {
            setTitles(titles.filter((_, i) => i !== index));
        }
    };

    const fileInputRef = React.useRef<HTMLInputElement | null>(null);

    const handleAddImageClick = () => {
        fileInputRef.current?.click();
    };

    const handleFilesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (!files) return;
        const newImages: string[] = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const url = URL.createObjectURL(file);
            newImages.push(url);
        }

        setImages((prev) => [...prev, ...newImages]);
        e.target.value = '';
    };

    const handleCreate = () => {
        const validTitles = titles.filter((t) => t.trim());
        if (validTitles.length === 0) {
            alert('Please enter at least one title');
            return;
        }
        if (images.length === 0) {
            alert('Please add at least one image');
            return;
        }
        router.push('/home');

        setTitles(['']);
        setImages([]);
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            <h2 className="text-2xl font-semibold mb-4">Add New Hero</h2>

            <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                    <label className="block font-medium">Titles *</label>
                    <Button
                        onClick={handleAddTitle}
                        className="px-3 py-1 text-sm bg-blue-600  rounded hover:bg-blue-700"
                    >
                        Add Title
                    </Button>
                </div>
                {titles.map((title, index) => (
                    <div key={index} className="flex items-center gap-2 mb-2">
                        <input
                            type="text"
                            className="flex-1 border rounded px-3 py-2"
                            value={title}
                            onChange={(e) => handleTitleChange(index, e.target.value)}
                            placeholder={`Enter title ${index + 1}`}
                        />
                        {titles.length > 1 && (
                            <button
                                type="button"
                                onClick={() => handleRemoveTitle(index)}
                                className="px-2 py-2 text-red-600 hover:bg-red-50 rounded"
                            >
                                ✕
                            </button>
                        )}
                    </div>
                ))}
            </div>

            <div className="mb-6">
                <label className="block mb-1 font-medium">Images</label>
                <button
                    onClick={handleAddImageClick}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 mb-2"
                >
                    Add Images
                </button>
                <input
                    type="file"
                    accept="image/*"
                    multiple
                    ref={fileInputRef}
                    onChange={handleFilesChange}
                    className="hidden"
                />

                <div className="mt-4 grid grid-cols-3 gap-4">
                    {images.map((src, idx) => (
                        <div key={idx} className="relative">
                            <Image
                                src={src}
                                alt={`Uploaded ${idx}`}
                                width={150}
                                height={100}
                                className="w-full h-32 object-cover rounded"
                            />
                            <button
                                onClick={() => {
                                    setImages(images.filter((_, i) => i !== idx));
                                    URL.revokeObjectURL(src);
                                }}
                                className="absolute top-1 right-1 text-white bg-black bg-opacity-50 rounded-full p-1"
                            >
                                ✕
                            </button>
                        </div>
                    ))}
                </div>
            </div>

            <div className="mt-6 flex space-x-2">
                <button
                    onClick={handleCreate}
                    className="px-4 py-2 rounded text-white bg-green-600 hover:bg-green-700"
                >
                    Create
                </button>
                <Link href="/home">
                    <button className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500">
                        Cancel
                    </button>
                </Link>
            </div>
        </div>
    );
};

export default CreateHeroPage;
