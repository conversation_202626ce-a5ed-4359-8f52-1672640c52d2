'use client';

import React, { useRef, useState, useEffect } from 'react';

interface ImageUploadProps {
  value?: string | null; 
  onChange?: (imageUrl: string | null) => void;
  accept?: string; 
  label?: string;
  disabled?: boolean;
  className?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value = null,
  onChange,
  accept = 'image/*',
  label = 'Upload Image',
  disabled = false,
  className = '',
}) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(value);

  useEffect(() => {
    setPreviewUrl(value ?? null);
  }, [value]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      return;
    }

    const file = files[0];
    const objectUrl = URL.createObjectURL(file);

    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }

    setPreviewUrl(objectUrl);
    onChange?.(objectUrl);
    e.target.value = '';
  };

  const handleClearImage = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    onChange?.(null);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`image-upload-component ${className}`}>
      <label className="block mb-1 font-medium">{label}</label>
      <div className="flex items-center gap-4">
        {previewUrl ? (
          <div className="relative">
            <img
              src={previewUrl}
              alt="Uploaded"
              className="w-40 h-28 object-cover rounded border"
            />
            {!disabled && (
              <button
                type="button"
                aria-label="Remove image"
                onClick={handleClearImage}
                className="absolute top-1 right-1 bg-black bg-opacity-60 rounded-full p-1 text-white hover:bg-opacity-80"
              >
                ✕
              </button>
            )}
          </div>
        ) : (
          <div className="w-40 h-28 flex items-center justify-center border rounded bg-gray-100 text-gray-400">
            No image
          </div>
        )}
        <div>
          <button
            type="button"
            onClick={handleUploadClick}
            disabled={disabled}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
          >
            {previewUrl ? 'Change Image' : 'Upload Image'}
          </button>
          <input
            type="file"
            accept={accept}
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            disabled={disabled}
          />
        </div>
      </div>
    </div>
  );
};

export default ImageUpload;
