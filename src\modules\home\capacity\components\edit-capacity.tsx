'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import ImageUpload from '@/components/image/image-upload';

interface CapacityItem {
  id: string;
  title: string;
  imageUrl: string | null;
  subtitle: string;
  link: string;
}

interface OurCapacity {
  mainTitle: string;
  capacities: CapacityItem[];
}

const initialData: OurCapacity = {
  mainTitle: 'Our Capabilities & Services',
  capacities: [
    {
      id: '1',
      title: 'Capacity A',
      imageUrl: '/images/image2.webp',
      subtitle: 'High quality service',
      link: 'https://example.com/capacity-a',
    },
    {
      id: '2',
      title: 'Capacity B',
      imageUrl: '/images/image4.webp',
      subtitle: 'Experienced team',
      link: 'https://example.com/capacity-b',
    },
  ],
};

const OurCapacityEditPage: React.FC = () => {
  const router = useRouter();
  const [mainTitle, setMainTitle] = useState(initialData.mainTitle);
  const [capacities, setCapacities] = useState<CapacityItem[]>(initialData.capacities);

  const fileInputRefs = useRef<Record<string, HTMLInputElement | null>>({});

  const handleMainTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMainTitle(e.target.value);
  };

  const handleCapacityChange = (
    id: string,
    field: keyof Omit<CapacityItem, 'id' | 'imageUrl'>,
    value: string
  ) => {
    setCapacities((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, [field]: value } : item
      )
    );
  };

  const handleAddCapacity = () => {
    const newCapacity: CapacityItem = {
      id: (capacities.length + 1 + Math.random()).toString(),
      title: '',
      imageUrl: null,
      subtitle: '',
      link: '',
    };
    setCapacities((prev) => [...prev, newCapacity]);
  };

  const handleRemoveCapacity = (id: string) => {
    setCapacities((prev) => prev.filter((item) => item.id !== id));
    delete fileInputRefs.current[id];
  };

  const handleUpdate = () => {
    if (!mainTitle.trim()) {
      alert('Main title is required');
      return;
    }
    for (const cap of capacities) {
      if (!cap.title.trim()) {
        alert('All capacity titles are required');
        return;
      }
      if (!cap.imageUrl) {
        alert('All capacity images are required');
        return;
      }
      if (!cap.subtitle.trim()) {
        alert('All capacity subtitles are required');
        return;
      }
      if (!cap.link.trim()) {
        alert('All capacity links are required');
        return;
      }
      try {
        new URL(cap.link);
      } catch {
        alert(`Invalid URL: ${cap.link}`);
        return;
      }
    }
    alert('Our Capacity updated (dummy)');
    router.push('/home/<USER>');
  };

  return (
    <div className="p-6 max-w-4xl mx-auto bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-6">Edit Our Capacity Section</h2>

      <div className="mb-6">
        <label className="block mb-1 font-medium">Main Title</label>
        <input
          type="text"
          value={mainTitle}
          onChange={handleMainTitleChange}
          className="w-full border rounded px-3 py-2"
          placeholder="Enter main title"
        />
      </div>

      <Button onClick={handleAddCapacity} className="mb-6 px-4 py-2">
        Add Capacity
      </Button>

      {capacities.map((cap, idx) => (
        <div
          key={cap.id}
          className="mb-8 p-4 border rounded bg-white shadow-sm relative"
        >
          <button
            type="button"
            onClick={() => handleRemoveCapacity(cap.id)}
            className="absolute top-2 right-2 text-red-600 hover:bg-red-100 rounded px-2"
            aria-label={`Remove capacity ${idx + 1}`}
          >
            ✕
          </button>

          <div className="mb-4">
            <label className="block mb-1 font-medium">Title</label>
            <input
              type="text"
              value={cap.title}
              onChange={(e) =>
                handleCapacityChange(cap.id, 'title', e.target.value)
              }
              className="w-full border rounded px-3 py-2"
              placeholder="Enter capacity title"
            />
          </div>

          <ImageUpload
            value={cap.imageUrl}
            onChange={(url) =>
              handleCapacityChange(cap.id, 'imageUrl', url)
            }
            label="Image"
          />

          <div className="mb-4">
            <label className="block mb-1 font-medium">Subtitle</label>
            <input
              type="text"
              value={cap.subtitle}
              onChange={(e) =>
                handleCapacityChange(cap.id, 'subtitle', e.target.value)
              }
              className="w-full border rounded px-3 py-2"
              placeholder="Enter capacity subtitle"
            />
          </div>

          <div className="mb-4">
            <label className="block mb-1 font-medium">Link</label>
            <input
              type="url"
              value={cap.link}
              onChange={(e) =>
                handleCapacityChange(cap.id, 'link', e.target.value)
              }
              className="w-full border rounded px-3 py-2"
              placeholder="Enter URL (https://example.com)"
            />
          </div>
        </div>
      ))}

      <div className="flex gap-4">
        <Button
          onClick={handleUpdate}
          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded"
        >
          Update
        </Button>

        <Link href="/home/<USER>">
          <button className="px-6 py-2 bg-gray-400 text-white rounded hover:bg-gray-500">
            Cancel
          </button>
        </Link>
      </div>
    </div>
  );
};

export default OurCapacityEditPage;
