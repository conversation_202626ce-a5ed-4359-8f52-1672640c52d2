"use client"

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Pen, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';

const dummyHero = {
    titles: ['Welcome to Our Site', 'Your Success Starts Here'],
    images: [
        '/images/image2.webp',
        '/images/image4.webp',
    ],
};

const HeroSection: React.FC = () => {
    const [hero, setHero] = useState<{ titles: string[]; images: string[]; } | null>(dummyHero);
    const router = useRouter();

    const handleDelete = () => {
        if (!hero) return;
        const confirmed = window.confirm('Are you sure you want to delete the hero?');
        if (confirmed) {
            setHero(null);
            alert('Hero deleted successfully');
        }
    };

    if (!hero) {
        return (
            <div className="p-6">
                <p className="mb-6">No hero section found.</p>
                <button
                    className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
                    onClick={() =>
                        setHero(dummyHero) 
                    }
                >
                    Create Hero Section
                </button>
            </div>
        );
    }

    return (
        <section className="p-6">
            <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-3xl font-bold">Hero Section</h1>
                    <Button onClick={() => router.push('/home/<USER>/edit')}>Edit</Button>
                </div>
                <div className="bg-white rounded-lg shadow">
                    <table className="min-w-full text-left border-separate border-spacing-0">
                        <thead>
                            <tr className="bg-gray-100">
                                <th className="border px-4 py-2">SN</th>
                                <th className="border py-3 px-4">Titles</th>
                                <th className="border py-3 px-4">Images</th>
                                <th className="border py-3 px-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr className="border-t last:border-b hover:bg-gray-100 transition">
                                <td className="border py-2 px-4">1</td>
                                <td className="border py-2 px-4">
                                    {hero.titles.map((title, i) => (
                                        <div key={i}>{title}</div>
                                    ))}
                                </td>
                                <td className="border py-2 px-4">
                                    <div className="flex gap-2">
                                        {hero.images.map((img, i) => (
                                            <Image
                                                key={i}
                                                src={img}
                                                alt={`Hero image ${i}`}
                                                width={60}
                                                height={40}
                                                className="rounded object-cover"
                                                style={{ width: '60px', height: '40px' }}
                                            />
                                        ))}
                                    </div>
                                </td>
                                <td className="py-2 px-4 flex gap-2">
                                    <Link
                                        href="/home/<USER>/edit"
                                        className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                                    >
                                        <Pen className="w-4 h-4" />
                                    </Link>
                                    <button
                                        onClick={handleDelete}
                                        className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                        type="button"
                                    >
                                        <Trash className="w-4 h-4" />
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    );
};

export default HeroSection;
